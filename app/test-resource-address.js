// Test script to verify resource account address calculation
import pkg from 'js-sha3';
const { sha3_256 } = pkg;

function deriveResourceAccountAddress(sourceAddress, seed) {
  try {
    // Remove 0x prefix if present
    const cleanAddress = sourceAddress.startsWith('0x') ? sourceAddress.slice(2) : sourceAddress;

    // Ensure address is 32 bytes (64 hex characters)
    const paddedAddress = cleanAddress.padStart(64, '0');

    // Convert address to bytes
    const addressBytes = new Uint8Array(32);
    for (let i = 0; i < 32; i++) {
      addressBytes[i] = parseInt(paddedAddress.slice(i * 2, i * 2 + 2), 16);
    }

    // Convert seed to bytes
    const seedBytes = new TextEncoder().encode(seed);

    // Combine address + seed + scheme (0xFF for resource account)
    const combined = new Uint8Array(addressBytes.length + seedBytes.length + 1);
    combined.set(addressBytes, 0);
    combined.set(seedBytes, addressBytes.length);
    combined[combined.length - 1] = 0xFF; // Resource account scheme

    // Calculate SHA3-256 hash
    const hash = sha3_256(combined);

    // Return as 0x-prefixed hex string
    return '0x' + hash;
  } catch (error) {
    console.error("Error deriving resource account address:", error);
    throw new Error(`Failed to derive resource account address: ${error}`);
  }
}

// Test with some example values
const testAddress = "0xc84f477c01610390eb55eae0dc77bfca55b1f1938f39a4b76c1c46c32b8ac416";
const testSeed = "test_seed";

console.log("Source Address:", testAddress);
console.log("Seed:", testSeed);
console.log("Derived Resource Account Address:", deriveResourceAccountAddress(testAddress, testSeed));

// Test with the address from the error message
const errorKioskAddress = "0xc84f477c01610390eb55eae0dc77bfca55b1f1938f39a4b76c1c46c32b8ac416";
console.log("\nError Kiosk Address:", errorKioskAddress);
console.log("This should be the derived address, not the source address");
