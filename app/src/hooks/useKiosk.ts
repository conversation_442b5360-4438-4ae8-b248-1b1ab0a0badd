import { useWallet } from "@aptos-labs/wallet-adapter-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAptos } from "./useAptos";
import {
  FUNCTIONS,
  VIEW_FUNCTIONS,
  STORAGE_KEYS
} from "@/utils/constants";
import { aptToOctas } from "@/lib/utils";
import { useToast } from "./useToast";

export interface Listing {
  object_addr: string;
  price: number; // in octas
}

export function useKiosk() {
  const { account, signAndSubmitTransaction } = useWallet();
  const aptos = useAptos();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const kioskAddress = localStorage.getItem(STORAGE_KEYS.KIOSK_ADDRESS);

  // Create kiosk mutation
  const createKioskMutation = useMutation({
    mutationFn: async (seed: string) => {
      if (!account) throw new Error("Wallet not connected");

      console.log("Creating kiosk with seed:", seed);
      console.log("Account address:", account.address);

      // Check if this seed was already used
      const existingSeed = localStorage.getItem(`${STORAGE_KEYS.KIOSK_ADDRESS}_seed`);
      if (existingSeed === seed) {
        throw new Error(`Seed "${seed}" has already been used. Please use a different seed.`);
      }

      const payload = {
        function: FUNCTIONS.CREATE_KIOSK as `${string}::${string}::${string}`,
        functionArguments: [seed],
      };

      console.log("Submitting transaction with payload:", payload);

      try {
        const response = await signAndSubmitTransaction({ data: payload });
        console.log("Transaction submitted:", response.hash);

        await aptos.waitForTransaction({ transactionHash: response.hash });
        console.log("Transaction confirmed");

        // For now, we'll use the owner address as the kiosk address
        // In a production app, you'd need to:
        // 1. Parse the transaction events to get the actual resource account address
        // 2. Or use a proper resource account derivation method
        // 3. Or store the mapping in your backend/indexer

        // Store the seed for reference
        localStorage.setItem(`${STORAGE_KEYS.KIOSK_ADDRESS}_seed`, seed);
        localStorage.setItem(STORAGE_KEYS.KIOSK_ADDRESS, account.address);

        return account.address;
      } catch (error) {
        console.error("Transaction failed:", error);
        throw error;
      }
    },
    onSuccess: (kioskAddr) => {
      toast({
        title: "Kiosk Created",
        description: `Kiosk created at ${kioskAddr.slice(0, 10)}...`,
      });
      queryClient.invalidateQueries({ queryKey: ["kiosk"] });
    },
    onError: (error) => {
      console.error("Create kiosk error:", error);
      let errorMessage = error.message;

      if (errorMessage.includes("claimed account")) {
        errorMessage = "This seed has already been used. Please try a different seed or clear your stall data.";
      }

      toast({
        title: "Failed to Create Stall",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  // List item mutation
  const listItemMutation = useMutation({
    mutationFn: async ({ objectId, price }: { objectId: string; price: number }) => {
      if (!account || !kioskAddress) throw new Error("Wallet not connected or no kiosk");

      const payload = {
        function: FUNCTIONS.LIST_ITEM as `${string}::${string}::${string}`,
        typeArguments: ["0x1::object::ObjectCore"], // Generic object type
        functionArguments: [kioskAddress, objectId, aptToOctas(price)],
      };

      const response = await signAndSubmitTransaction({ data: payload });
      await aptos.waitForTransaction({ transactionHash: response.hash });
      return response;
    },
    onSuccess: () => {
      toast({
        title: "Item Listed",
        description: "Your item has been listed successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["listings"] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Buy item mutation
  const buyItemMutation = useMutation({
    mutationFn: async ({ objectAddr, price }: { objectAddr: string; price: number }) => {
      if (!account || !kioskAddress) throw new Error("Wallet not connected");

      const payload = {
        function: FUNCTIONS.BUY as `${string}::${string}::${string}`,
        typeArguments: ["0x1::object::ObjectCore"],
        functionArguments: [kioskAddress, objectAddr, price], // price in octas
      };

      const response = await signAndSubmitTransaction({ data: payload });
      await aptos.waitForTransaction({ transactionHash: response.hash });
      return response;
    },
    onSuccess: () => {
      toast({
        title: "Purchase Successful",
        description: "Item purchased successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["listings"] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Check if kiosk exists
  const kioskQuery = useQuery({
    queryKey: ["kiosk", kioskAddress],
    queryFn: async () => {
      if (!kioskAddress) return null;

      try {
        const owner = await aptos.view({
          payload: {
            function: VIEW_FUNCTIONS.GET_KIOSK_OWNER as `${string}::${string}::${string}`,
            functionArguments: [kioskAddress],
          },
        });
        return owner[0] ? { address: kioskAddress, owner: owner[0] } : null;
      } catch {
        return null;
      }
    },
    enabled: !!kioskAddress,
  });

  // Function to clear kiosk data (for debugging)
  const clearKioskData = () => {
    localStorage.removeItem(STORAGE_KEYS.KIOSK_ADDRESS);
    localStorage.removeItem(`${STORAGE_KEYS.KIOSK_ADDRESS}_seed`);
    queryClient.invalidateQueries({ queryKey: ["kiosk"] });
  };

  return {
    kioskAddress,
    kiosk: kioskQuery.data,
    isKioskLoading: kioskQuery.isLoading,
    createKiosk: createKioskMutation.mutate,
    isCreatingKiosk: createKioskMutation.isPending,
    listItem: listItemMutation.mutate,
    isListingItem: listItemMutation.isPending,
    buyItem: buyItemMutation.mutate,
    isBuyingItem: buyItemMutation.isPending,
    clearKioskData, // For debugging purposes
  };
}
