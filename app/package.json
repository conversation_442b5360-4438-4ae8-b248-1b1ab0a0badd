{"name": "kiosk-dapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\""}, "dependencies": {"@aptos-labs/ts-sdk": "^1.28.0", "@aptos-labs/wallet-adapter-ant-design": "^2.0.4", "@aptos-labs/wallet-adapter-react": "^3.6.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "js-sha3": "^0.9.3", "lucide-react": "^0.441.0", "petra-plugin-wallet-adapter": "^0.4.5", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.47", "prettier": "^3.3.3", "tailwindcss": "^3.4.12", "typescript": "^5.2.2", "vite": "^5.3.4"}}