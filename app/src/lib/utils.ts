import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatApt(octas: number | string): string {
  const octasNum = typeof octas === 'string' ? parseInt(octas) : octas;
  return (octasNum / 100_000_000).toFixed(4);
}

export function aptToOctas(apt: number): number {
  return Math.floor(apt * 100_000_000);
}

export function truncateAddress(address: string | undefined | null, start = 6, end = 4): string {
  if (!address || typeof address !== 'string') return 'N/A';
  if (address.length <= start + end) return address;
  return `${address.slice(0, start)}...${address.slice(-end)}`;
}

/**
 * Derives a resource account address from a source address and seed
 * This is a placeholder implementation - the actual address will be obtained from transaction events
 */
export function deriveResourceAccountAddress(sourceAddress: string, _seed: string): string {
  // For now, we'll return the source address as a fallback
  // The actual resource account address should be obtained from the KioskCreated event
  console.warn("deriveResourceAccountAddress is a fallback method. Use transaction events for accurate address.");
  return sourceAddress;
}
