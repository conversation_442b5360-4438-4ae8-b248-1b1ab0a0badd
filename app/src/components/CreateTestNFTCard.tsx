import { useState } from "react";
import { useWallet } from "@aptos-labs/wallet-adapter-react";
import { useAptos } from "@/hooks/useAptos";
import { Account } from "@aptos-labs/ts-sdk";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/useToast";
import { MODULE_ADDRESS } from "@/utils/constants";
import { Sparkles, Plus } from "lucide-react";

export function CreateTestNFTCard() {
  const { account, connected, signAndSubmitTransaction } = useWallet();
  const aptos = useAptos();
  const { toast } = useToast();
  const [isCreating, setIsCreating] = useState(false);
  const [nftName, setNftName] = useState("");
  const [nftDescription, setNftDescription] = useState("");

  const createTestNFT = async () => {
    if (!account || !nftName.trim()) return;

    setIsCreating(true);
    try {
      const collectionName = "Test Collection";
      const tokenName = nftName.trim();
      const tokenDescription = nftDescription.trim() || "A test NFT for marketplace testing";

      const createCollectionPayload = {
        function: "0x4::aptos_token::create_collection" as `${string}::${string}::${string}`,
        typeArguments: [],
        functionArguments: [
          collectionName, // name
          tokenDescription, // description
          "https://via.placeholder.com/300x300.png?text=" + encodeURIComponent(tokenName), // uri
          "1000000", // maximum
          true, // mutable_description
          true, // mutable_royalty
          true, // mutable_uri
          true, // mutable_token_properties
          true, // mutable_collection_properties
          true, // royalty_present
          account!.address, // royalty_payee
          5, // royalty_numerator (e.g., 5%)
          100, // royalty_denominator
          [], // property_keys
          [], // property_values
          [], // property_types
        ],
      };


      let response = await signAndSubmitTransaction({ data: createCollectionPayload });
      await aptos.waitForTransaction({ transactionHash: response.hash });

      const mintTokenPayload = {
        function: "0x4::aptos_token::mint" as `${string}::${string}::${string}`,
        typeArguments: [],
        functionArguments: [
          collectionName,
          tokenDescription,
          tokenName,
          "https://via.placeholder.com/300x300.png?text=" + encodeURIComponent(tokenName),
          [],
          [],
          [],
        ],
      };

      response = await signAndSubmitTransaction({ data: mintTokenPayload });
      console.log("NFT minting transaction:", response.hash);

      await aptos.waitForTransaction({ transactionHash: response.hash });

      toast({
        title: "Test NFT Created Successfully!",
        description: `"${tokenName}" has been minted as a standard Digital Asset. Check your assets above.`,
      });

      // Clear form
      setNftName("");
      setNftDescription("");

      // Refresh the page to update assets
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error: any) {
      console.error("Error creating test NFT:", error);
      toast({
        title: "Creation Failed",
        description: error.message || "Unable to create test NFT. Please check the console for more details.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  if (!connected) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="h-5 w-5" />
            <span>Create Test NFT</span>
          </CardTitle>
          <CardDescription>
            Connect your wallet to create test NFTs
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Sparkles className="h-5 w-5" />
          <span>Create Test NFT</span>
        </CardTitle>
        <CardDescription>
          Create a test NFT to practice listing items
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="nftName">NFT Name</Label>
          <Input
            id="nftName"
            placeholder="My Test NFT"
            value={nftName}
            onChange={(e) => setNftName(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="nftDescription">Description (Optional)</Label>
          <Input
            id="nftDescription"
            placeholder="A test NFT for the marketplace"
            value={nftDescription}
            onChange={(e) => setNftDescription(e.target.value)}
          />
        </div>

        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-xs text-yellow-800">
            <strong>注意：</strong> 这会在测试网上创建一个真实的 NFT。创建后你就可以在上方的资产列表中看到它，然后将其列出销售。
          </p>
        </div>

        <Button
          onClick={createTestNFT}
          disabled={!nftName.trim() || isCreating}
          className="w-full"
        >
          <Plus className="mr-2 h-4 w-4" />
          {isCreating ? "Creating..." : "Create Test NFT"}
        </Button>

        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>替代方案：</strong></p>
          <p>• 如果创建失败，你可以使用现有的数字资产</p>
          <p>• 或者在其他 Aptos NFT 平台上创建 NFT</p>
          <p>• 然后在 "Manual Entry" 标签中输入 Object ID</p>
        </div>
      </CardContent>
    </Card>
  );
}
